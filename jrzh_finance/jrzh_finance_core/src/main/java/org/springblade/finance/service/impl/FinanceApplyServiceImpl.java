/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.finance.service.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.RepaymentConstant;
import org.springblade.common.enums.CustomerGoodsEnum;
import org.springblade.common.enums.EnterpriseQuotaStatusEnum;
import org.springblade.common.enums.EnterpriseTypeEnum;
import org.springblade.common.enums.ProcessTypeEnum;
import org.springblade.common.utils.Condition;
import org.springblade.common.utils.StreamUtil;
import org.springblade.common.utils.ThreadUtils;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseEntity;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.constant.FeignConstants;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.StringPool;
import org.springblade.customer.entity.*;
import org.springblade.customer.feign.RemoteCustomerGoodsService;
import org.springblade.customer.feign.RemoteCustomerInfo;
import org.springblade.customer.feign.RemoteEnterpriseQuotaService;
import org.springblade.customer.vo.SalesContractVO;
import org.springblade.finance.dto.FinanceApplyDTO;
import org.springblade.finance.entity.FinanceApply;
import org.springblade.finance.entity.LoanApply;
import org.springblade.finance.enums.financeApply.FinanceApplyEnums;
import org.springblade.finance.excel.FinanceApplyExcel;
import org.springblade.finance.external.handler.enterpriseQuota.FinanceEnterpriseService;
import org.springblade.finance.external.handler.salesContract.FinanceSalesContractService;
import org.springblade.finance.handler.FinanceApplyCommonService;
import org.springblade.finance.mapper.FinanceApplyMapper;
import org.springblade.finance.service.IFinanceApplyService;
import org.springblade.finance.service.ILoanApplyService;
import org.springblade.finance.vo.*;
import org.springblade.finance.vo.financeCommon.FinanceApplyCommonVo;
import org.springblade.finance.wrapper.FinanceApplyWrapper;
import org.springblade.loan.entity.*;
import org.springblade.loan.feign.*;
import org.springblade.loan.vo.LoanManageRepaymentPlanVO;
import org.springblade.loan.vo.LoanManageRepaymentVO;
import org.springblade.loan.wrapper.LoanManageRepaymentWrapper;
import org.springblade.loan.wrapper.RepaymentFeeWrapper;
import org.springblade.loan.wrapper.RepaymentPlanFeeWrapper;
import org.springblade.process.entity.BusinessProcessProgress;
import org.springblade.process.service.IBusinessProcessProgressService;
import org.springblade.process.service.IBusinessProcessService;
import org.springblade.product.common.entity.Product;
import org.springblade.product.expense.constant.ExpenseConstant;
import org.springblade.product.moudle.pubproduct.service.impl.ProductDirector;
import org.springblade.system.entity.Dept;
import org.springblade.system.entity.User;
import org.springblade.system.feign.RemoteDeptSearchService;
import org.springblade.system.feign.RemoteUserService;
import org.springblade.system.utils.UserUtils;
import org.springblade.workflow.core.constant.WfProcessConstant;
import org.springblade.workflow.process.model.WfProcess;
import org.springblade.workflow.process.service.IWfProcessService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;


/**
 * 融资申请 服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-18
 */
@Service
@RequiredArgsConstructor
public class FinanceApplyServiceImpl extends BaseServiceImpl<FinanceApplyMapper, FinanceApply> implements IFinanceApplyService {


    /**
     * 融资通用接口
     */
    private final Map<String, FinanceApplyCommonService> financeApplyCommonMap;

    private final FinanceSalesContractService salesContractDetailService;

    private final FinanceEnterpriseService enterpriseQuotaService;

    private final IBusinessProcessProgressService businessProcessProgressService;

    private final RemoteCustomerGoodsService customerGoodsService;
    private final RemoteUserService remoteUserService;
    private final RemoteLoanManageRepaymentPlan remoteLoanManageRepaymentPlan;
    private final RemoteLoanManageIou remoteLoanManageIou;
    private final ILoanApplyService loanApplyService;
    private final IBusinessProcessService businessProcessService;
    private final IWfProcessService processService;
    private final RemoteEnterpriseQuotaService remoteEnterpriseQuotaService;
    private final RemoteRepaymentPlanFee remoteRepaymentPlanFee;
    private final RemoteDeptSearchService remoteDeptSearchService;
    private final RemoteLoanManageRepayment remoteLoanManageRepayment;
    private final RemoteRepaymentFee remoteRepaymentFee;
    private final RemoteCustomerInfo remoteCustomerInfo;
    private final ProductDirector productDirector;


    /**
     * 自定义分页
     *
     * @param financeApplyDTO 融资申请DTO对象
     * @param query           分页对象
     * @return page
     */
    @Override
    public IPage<FinanceApplyVO> selectFinanceApplyPage(FinanceApplyDTO financeApplyDTO, Query query) {
        IPage<FinanceApply> page = baseMapper.selectPage(Condition.getPage(query),
                Condition.getQueryWrapper(financeApplyDTO, FinanceApply.class).lambda()
                        .in(FinanceApply::getGoodsType, productDirector.getAllType())
                        .orderByDesc(FinanceApply::getCreateTime));
        IPage<FinanceApplyVO> pageVO = FinanceApplyWrapper.build().pageVO(page);
        if (page.getTotal() <= 0) {
            return pageVO;
        }
        List<FinanceApply> records = page.getRecords();
        List<Long> userIds = StreamUtil.map(records, FinanceApply::getUserId);
        Map<Long, User> userMap = remoteUserService.listByUser(userIds,FeignConstants.FROM_IN).getData().stream().collect(Collectors.toMap(User::getId, e -> e));
        List<String> financeNoList = StreamUtil.map(records, FinanceApply::getFinanceNo);
        List<LoanManageIou> loanManageIouList = remoteLoanManageIou.getListInfo(financeNoList).getData();
        Map<String, Long> loanIouMap = StreamUtil.toMap(loanManageIouList, LoanManageIou::getFinanceNo, LoanManageIou::getId);
        List<Long> financeIds = StreamUtil.map(records, FinanceApply::getId);
        //未结束的还款计划状态
        List<LoanManageRepaymentPlan> list = remoteLoanManageRepaymentPlan.getListInfo(financeIds).getData();
        Map<String, List<LoanManageRepaymentPlan>> allUnRepaymentMap = MapUtil.newHashMap();
        Map<String, List<LoanManageRepaymentPlan>> overdueRepaymentMap = MapUtil.newHashMap();
        if (CollectionUtil.isNotEmpty(list)) {
            allUnRepaymentMap = list.stream().collect(Collectors.groupingBy(e -> e.getFinanceApplyId().toString()));
            overdueRepaymentMap = list.stream().filter(e -> RepaymentConstant.RepaymentPlanStatusEnum.OVERDUE_UN_PAY.getCode() == e.getStatus()).collect(Collectors.groupingBy(e -> e.getFinanceApplyId().toString()));
        }
        //对产品类型分组
        Map<Integer, List<FinanceApplyVO>> goodsTypeMap = pageVO.getRecords().stream().collect(Collectors.groupingBy(FinanceApplyVO::getGoodsType));
        //分组处理
        for (Map.Entry<Integer, List<FinanceApplyVO>> goodsOfFinanceApply : goodsTypeMap.entrySet()) {
            List<FinanceApplyVO> financeApplyVOList = goodsOfFinanceApply.getValue();
            Integer goodsType = goodsOfFinanceApply.getKey();
            if (ObjectUtil.isEmpty(goodsType)) {
                continue;
            }
            FinanceApplyCommonService service = financeApplyCommonMap.get(FinanceApplyEnums.getFinanceCommonTypeName(goodsType));
            if (ObjectUtil.isNotNull(service)) {
                service.dealFinanceApply(financeApplyVOList, allUnRepaymentMap, overdueRepaymentMap, userMap);
            }
            financeApplyVOList.forEach(e -> e.setIouId(loanIouMap.get(e.getFinanceNo())));
        }
        //合并
        pageVO.setRecords(goodsTypeMap.values().stream().flatMap(List::stream).collect(Collectors.toList()));
        return pageVO;
    }

    /**
     * 根据融资id，查询融资详情数据
     *
     * @param id 融资id
     * @return 融资详情数据
     */
    @Override
    public FinanceApplyCommonVo financeDetail(Long id) {
        //查询出融资数据，然后判断产品类型
        FinanceApply financeApply = this.getById(id);
        FinanceApplyCommonService financeApplyCommonService = financeApplyCommonMap.get(FinanceApplyEnums.getFinanceCommonTypeName(financeApply.getGoodsType()));
        return financeApplyCommonService.financeDetail(financeApply);
    }

    /**
     * 根据融资ids，查询融资详情数据
     *
     * @param ids 融资id列表
     * @return 融资详情数据
     */
    @Override
    public List<FinanceApplyCommonVo> financeDetailList(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            throw new ServiceException("融资id列表为空");
        }
        List<FinanceApply> financeApplies = this.listByIds(ids);
        if (CollUtil.isEmpty(financeApplies)) {
            throw new ServiceException("未查询到融资申请数据");
        }
        FinanceApplyCommonService service = financeApplyCommonMap.get(FinanceApplyEnums.getFinanceCommonTypeName(financeApplies.get(0).getGoodsType()));

        return CollStreamUtil.toList(financeApplies,service::financeDetail);
    }

    /**
     * 根据融资编号查询
     *
     * @param financeNo 融资编号
     * @return FinanceApply
     */
    @Override
    public FinanceApply getByFinanceNo(String financeNo) {
        return baseMapper.selectOne(Wrappers.<FinanceApply>lambdaQuery().eq(FinanceApply::getFinanceNo, financeNo));
    }

    @Override
    public void processTerminalReturnAmount(Long financeApplyId) {
        List<SalesContractDetail> salesContractDetailList = salesContractDetailService.getByFinanceApplyId(financeApplyId);
        Map<Long, BigDecimal> decimalMap = StreamUtil.toMap(salesContractDetailList, SalesContractDetail::getSaleContractId, SalesContractDetail::getAmount);
        Set<Long> longs = decimalMap.keySet();

        List<Long> ids = Convert.convert(new TypeReference<List<Long>>() {
        }, longs);

        List<SalesContract> salesContracts = salesContractDetailService.selectListByIds(ids);
        salesContracts.forEach(salesContract -> {
            BigDecimal amount = decimalMap.get(salesContract.getId());
            BigDecimal financingAvailableAmount = salesContract.getFinancingAvailableAmount();
            salesContract.setFinancingAvailableAmount(financingAvailableAmount.add(amount));
            salesContract.setFinancingApplyAmount(salesContract.getFinancingApplyAmount().subtract(amount));
        });
        // 资方额度
        BigDecimal amount = decimalMap.values().stream().reduce(BigDecimal.ZERO, BigDecimal::add);
        FinanceApply financeApply = baseMapper.selectById(financeApplyId);
        enterpriseQuotaService.returnApplyAmount(amount, financeApply.getGoodsId(), financeApply.getUserId(), EnterpriseTypeEnum.FINANCING_ENTERPRISE.getCode());
        // 更新销售合同
        salesContractDetailService.updateBatchById(salesContracts);
    }

    /**
     * 根据客户产品id获取融资ids
     *
     * @param customerGoodsIds 客户产品ids
     * @return
     */
    private List<Long> listByCustomerGoodsId(List<Long> customerGoodsIds) {
        return list(Wrappers.<FinanceApply>lambdaQuery().in(FinanceApply::getCustomerGoodsId, customerGoodsIds))
                .stream().map(FinanceApply::getId).distinct().collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void closeAllRelatedReceivable(List<Long> customerGoodsIds) {
        //查询所有需要关闭的应收融资申请业务
        List<Long> financeIds = listByCustomerGoodsId(customerGoodsIds);
        if (CollUtil.isEmpty(financeIds)) {
            return;
        }
        List<Integer> needCloseProcessType = Arrays.asList(ProcessTypeEnum.FINANCE_APPLY.getCode(),
                ProcessTypeEnum.LOAN_APPLY.getCode(), ProcessTypeEnum.AUTO_LOAN_FINANCE_APPLY.getCode());
        List<BusinessProcessProgress> businessProcessProgresses = businessProcessProgressService.listRunningByProcessTypeByFinanceIds(needCloseProcessType, financeIds);
        //关闭正在运行的融资申请
        businessProcessProgressService.closeFinanceApply(businessProcessProgresses);
        // 关闭正在调整额度申请
        closeQuotaAdjust(customerGoodsIds);
    }

    @Override
    public ApproveInfo selectBackApproveInfo(Long id) throws ExecutionException, InterruptedException, TimeoutException {
        FinanceApply financeApply = baseMapper.selectById(id);
        LoanApply loanApply = loanApplyService.getByFinanceApplyId(id);

        String financeApplyProcessInstanceId = financeApply.getProcessInstanceId();
        // 查询融资申请任务编号
        CompletableFuture<String> financeApplyFuture = ThreadUtils.supplyAsync(() -> (String) businessProcessService.getSingleVariable(financeApplyProcessInstanceId, WfProcessConstant.PROCESS_NO));
        // 查询融资申请流转信息
        Future<List<WfProcess>> financeFlowList = processService.historyFlowList(financeApplyProcessInstanceId, null, null);
        String loanTaskNo = null;
        List<WfProcess> loanFlowList = null;
        if (Objects.nonNull(loanApply)) {
            String loanApplyProcessInstanceId = loanApply.getProcessInstanceId();
            // 查询放款申请任务编号
            CompletableFuture<String> loanApplyFuture = ThreadUtils.supplyAsync(() -> (String) businessProcessService.getSingleVariable(loanApplyProcessInstanceId, WfProcessConstant.PROCESS_NO));
            // 查询放款申请流转信息
            Future<List<WfProcess>> loanFlowListFuture = processService.historyFlowList(loanApplyProcessInstanceId, null, null);
            loanTaskNo = ThreadUtils.get(loanApplyFuture);
            loanFlowList = loanFlowListFuture.get();
        }
        return ApproveInfo.builder()
                .financeTaskNo(ThreadUtils.get(financeApplyFuture))
                .financeFlowList(financeFlowList.get())
                .loanTaskNo(loanTaskNo)
                .loanFlowList(loanFlowList)
                .build();
    }

    @Override
    public LoanInfo loanInfo(Long id) {
        // 查询融资申请
        FinanceApply financeApply = baseMapper.selectById(id);
        // 根据融资编号查询借据单
        LoanManageIou loanManageIou = remoteLoanManageIou.getByFinanceNo(financeApply.getFinanceNo()).getData();
        if (Objects.isNull(loanManageIou)) {
            return null;
        }
        String collectionAccount = (String) remoteEnterpriseQuotaService.getRepaymentAccountSuffix(financeApply.getGoodsId(), AuthUtil.getUserId()).getData();
        // 根据借据单号查询还款计划
        List<LoanManageRepaymentPlan> loanManageRepaymentPlans = remoteLoanManageRepaymentPlan.listByIouNo(loanManageIou.getIouNo()).getData();
        //List<LoanManageRepaymentPlan> loanManageRepaymentPlans = loanManageRepaymentPlanService.listUnRepaymentListByIouNo(loanManageIou.getIouNo());
        BigDecimal totalPlanAmount = BigDecimal.ZERO;
//        List<LoanManageRepaymentPlanVO> planVOS = LoanManageRepaymentPlanWrapper.build().listVO(loanManageRepaymentPlans);
        List<LoanManageRepaymentPlanVO> planVOS = BeanUtil.copyToList(loanManageRepaymentPlans, LoanManageRepaymentPlanVO.class);
        if (CollectionUtil.isNotEmpty(planVOS)) {
            for (LoanManageRepaymentPlanVO planVO : planVOS) {
                //查询还款计划动态费用
                List<RepaymentPlanFee> feePlans = remoteRepaymentPlanFee.getAllPlanFeeByPlanIds(planVO.getId()).getData();
                if (CollUtil.isEmpty(feePlans)) {
                    feePlans = Collections.emptyList();
                }
                //筛选出融资和正常还款节点数据
                feePlans = feePlans.stream().filter(e -> ExpenseConstant.FeeNodeEnum.FINANCE_APPLY.getCode().equals(e.getFeeNode())
                        || ExpenseConstant.FeeNodeEnum.NORMAL_REPAYMENT.getCode().equals(e.getFeeNode())
                        || ExpenseConstant.FeeNodeEnum.EXTENSION_APPLY.getCode().equals(e.getFeeNode())).collect(Collectors.toList());
                planVO.setRepaymentPlanFeeVOList(RepaymentPlanFeeWrapper.build().listVO(feePlans));
                BigDecimal feePlanAmount = feePlans.stream().map(RepaymentPlanFee::getPlanNeePayAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                totalPlanAmount = totalPlanAmount
                        .add(planVO.getPrincipal())
                        .add(planVO.getPlanInterest())
                        .add(planVO.getServiceFee())
                        .add(planVO.getPenaltyInterest()
                                .add(feePlanAmount == null ? BigDecimal.ZERO : feePlanAmount));
                planVO.setTotalAmount(totalPlanAmount);
            }
        }
        // 查询还款账户
        String repaymentAccount = (String) remoteEnterpriseQuotaService.getRepaymentAccountSuffix(financeApply.getGoodsId(), AuthUtil.getUserId()).getData();
        // 查询资方
        Dept dept = remoteDeptSearchService.getDeptById(financeApply.getCapitalId()).getData();
        // 查询融资企业
        User user = remoteUserService.getUserById(financeApply.getUserId(), FeignConstants.FROM_IN).getData();
        // 查询工商信息获取统一社会信用代码
        CustomerInfo customerInfo = remoteCustomerInfo.getByCompanyId(financeApply.getUserId()).getData();
        // 查询还款明细
        List<LoanManageRepayment> loanManageRepayments = remoteLoanManageRepayment.listAllRelationLoanRepayment(financeApply.getFinanceNo()).getData();
        List<LoanManageRepaymentVO> loanManageRepaymentVOS = LoanManageRepaymentWrapper.build().listVO(loanManageRepayments);
        //添加动态费用
        loanManageRepaymentVOS.forEach(repaymentVO ->
        {
            List<RepaymentFee> payRepaymentFeeList = remoteRepaymentFee.getPayRepaymentFeeList(repaymentVO.getId()).getData();
            repaymentVO.setRepaymentFeeList(RepaymentFeeWrapper.build().listVO(payRepaymentFeeList));
        });

        return LoanInfo.builder()
                .totalPlanAmount(totalPlanAmount)
                .annualInterestRate(financeApply.getAnnualInterestRate())
                .dailyInterestRate(financeApply.getDailyInterestRate())
                .loanAccount(collectionAccount)
                .repaymentAccount(repaymentAccount)
                .capitalName(dept.getDeptName())
                .userName(user.getName())
                .firstRepaymentDate(loanManageIou.getFirstRepaymentDate())
                .creditCode(Objects.nonNull(customerInfo) ? customerInfo.getBusinessLicenceNumber() : StringPool.EMPTY)
                .startDate(loanManageIou.getLoanTime())
                .endDate(loanManageIou.getExpireTime())
                .loanTerm(financeApply.getLoadTerm())
                .loanAmount(loanManageIou.getIouAmount())
                .loanTermUnit(financeApply.getLoadTermUnit())
                .repaymentMode(financeApply.getRepaymentMode())
                .goodsName(financeApply.getGoodsName())
                .goodsType(financeApply.getGoodsType())
                .loanUsage(financeApply.getLoanUsage())
                .repaymentList(loanManageRepaymentVOS)
                .repaymentPlanList(planVOS)
                .build();
    }

    @Override
    public IPage<FinanceApplyStatusBackVo> getPage(Long userId, Integer type, Query query) {
        IPage<FinanceApply> financeApplyIPage = baseMapper.selectPage(Condition.getPage(query), Wrappers.<FinanceApply>lambdaQuery()
                .eq(FinanceApply::getUserId, userId).eq(FinanceApply::getGoodsType, type).orderByDesc(BaseEntity::getCreateTime));
        List<FinanceApply> records = financeApplyIPage.getRecords();
        List<Long> goodsIds = records.stream().map(FinanceApply::getGoodsId).distinct().collect(Collectors.toList());
        if (org.springblade.core.tool.utils.ObjectUtil.isEmpty(goodsIds)) {
            return new Page<>();
        }
        Map<Long, Product> goodsIdMap = new HashMap<>();
        List<Product> list = productDirector.selectList(new HashMap<>(), goodsIds);
        if (org.springblade.core.tool.utils.ObjectUtil.isNotEmpty(list)) {
            goodsIdMap = StreamUtil.toMap(list, BaseEntity::getId, e -> e);
        }
        Map<Long, Product> finalGoodsIdMap = goodsIdMap;
        if (ObjectUtil.isEmpty(finalGoodsIdMap)) {
            return null;
        }
        List<FinanceApplyStatusBackVo> financeApplyStatusBackVoPage = records.stream().map(financeApply -> {
            FinanceApplyStatusBackVo financeApplyStatusBackVo = FinanceApplyWrapper.build().toFinanceApplyStatusBack(financeApply);
            String goodsName = finalGoodsIdMap.get(financeApply.getGoodsId()).getGoodsName();
            financeApplyStatusBackVo.setGoodsName(goodsName);
            return financeApplyStatusBackVo;
        }).collect(Collectors.toList());
        IPage<FinanceApplyStatusBackVo> page = new Page<>(financeApplyIPage.getCurrent(), financeApplyIPage.getSize(), financeApplyIPage.getTotal());
        page.setRecords(financeApplyStatusBackVoPage);
        page.setPages(financeApplyIPage.getPages());
        page.setTotal(financeApplyIPage.getTotal());
        return page;
    }

    @Override
    public Boolean changeFinanceStatus(String financeNo, Integer code) {
        return update(Wrappers.<FinanceApply>lambdaUpdate()
                .eq(FinanceApply::getFinanceNo, financeNo)
                .set(FinanceApply::getStatus, code));
    }

    @Override
    public FinanceApplyInfo selectFinanceApplyInfo(Long id) {
        FinanceApply financeApply = baseMapper.selectById(id);
        if (Objects.isNull(financeApply)) {
            return null;
        }
        FinanceApplyInfo financeApplyInfo = BeanUtil.copyProperties(financeApply, FinanceApplyInfo.class);
        Long capitalId = financeApplyInfo.getCapitalId();
        Dept data = remoteDeptSearchService.getDeptById(capitalId).getData();
        financeApplyInfo.setCapitalName(data.getDeptName());
        financeApplyInfo.setCapitalLogo(data.getLogoSrc());
        User user = UserUtils.getUserById(financeApplyInfo.getApplyUser());
        financeApplyInfo.setOperator(user.getName());
        financeApplyInfo.setBillingMethod(financeApply.getRepaymentMode().toString());
        //递归获取旧融资单号
        List<Long> financeIds = new ArrayList<>();
        selectFather(financeApply, financeIds);
        //获取旧融资编号
        FinanceApply oldFinanceApply = baseMapper.selectById(financeApply.getOldFinanceId());
        List<Long> financeApplyIds = new ArrayList<>(2);
        financeApplyIds.add(financeApply.getId());
        if (org.springblade.core.tool.utils.ObjectUtil.isNotEmpty(oldFinanceApply)) {
            financeApplyIds.add(oldFinanceApply.getId());
            financeApplyInfo.setOldFinanceApply(oldFinanceApply);
        }
        //递归查询质押品
        List<SalesContractDetail> salesContractDetailList = salesContractDetailService.getByFinanceApplyId(financeIds);
        financeApplyInfo.setSalesContractDetailList(salesContractDetailList);
        if (CollectionUtil.isNotEmpty(salesContractDetailList)) {
            List<SalesContractVO> salesContractList = salesContractDetailService.queryByIds(StreamUtil.map(salesContractDetailList, SalesContractDetail::getSaleContractId));
            financeApplyInfo.setSalesContractList(salesContractList);
        }
        return financeApplyInfo;
    }

    @Override
    public List<Long> listRelationFinanceIds(FinanceApply financeApply) {
        List<Long> result = new ArrayList<>();
        selectFather(financeApply.getOldFinanceId(), result);
        selectChild(financeApply.getId(), result);
        return result;
    }

    @Override
    public List<FinanceApplyExcel> export(FinanceApplyDTO financeApplyDTO) {
        List<FinanceApplyExcel> excelList = baseMapper.selectExportList(financeApplyDTO);
//        for (FinanceApplyExcel financeApplyExcel : excelList) {
//            for (FinanceApplyStatusEnum value : FinanceApplyStatusEnum.values()) {
//                if (value.getCode().equals(financeApplyExcel.getStatus())) {
//                    financeApplyExcel.setStatusStr(value.getName());
//                }
//            }
//        }
        return excelList;
    }

    private void selectFather(Long oldFinanceId, List<Long> result) {
        //查询数据库中对应id的实体类
        //查询符合条件的对象
        FinanceApply oldFinanceApply = getOne(Wrappers.<FinanceApply>lambdaQuery().eq(FinanceApply::getId, oldFinanceId));
        if (oldFinanceApply != null) {
            result.add(oldFinanceApply.getId());
            selectFather(oldFinanceApply.getOldFinanceId(), result);
        }
    }

    private void selectChild(Long id, List<Long> result) {
        FinanceApply financeApply = getOne(Wrappers.<FinanceApply>lambdaQuery().eq(FinanceApply::getOldFinanceId, id));
        if (financeApply != null) {
            result.add(financeApply.getId());
            selectChild(financeApply.getId(), result);
        }
    }

    private void selectFather(FinanceApply financeApply, List<Long> result) {
        result.add(financeApply.getId());
        if (CommonConstant.NO_EXIST.equals(financeApply.getOldFinanceId())) {
            return;
        }
        FinanceApply currentOld = getByFinanceNo(financeApply.getOldFinanceNo());
        selectFather(currentOld, result);
    }

    /**
     * 关闭正在调整额度申请
     *
     * @param customerGoodsIds
     */
    private void closeQuotaAdjust(List<Long> customerGoodsIds) {
        if (CollUtil.isNotEmpty(customerGoodsIds)) {
            List<CustomerGoods> customerGoods = customerGoodsService.listByIds(customerGoodsIds).getData();
            if (CollUtil.isNotEmpty(customerGoods)) {
                List<Long> quotaIds = customerGoods.stream().map(CustomerGoods::getEnterpriseQuotaId).collect(Collectors.toList());
                //查询正在调整额度的额度表
                List<EnterpriseQuota> enterpriseQuotas =
                        enterpriseQuotaService.queryEnterpriseQuotaByStatusAndQuotaIds
                                (EnterpriseQuotaStatusEnum.CHANGING.getCode(), quotaIds);

                if (CollUtil.isNotEmpty(enterpriseQuotas)) {

                    List<Long> changQuotaIds = enterpriseQuotas.stream().map(BaseEntity::getId).collect(Collectors.toList());
                    //修改额度表状态
                    enterpriseQuotaService.changeStatus(changQuotaIds, EnterpriseQuotaStatusEnum.FROZEN.getCode());
                    //修改客户产品状态
                    List<CustomerGoods> CustomerGoodsList = customerGoodsService.getCustomerGoodsByEnterpriseQuotaId(changQuotaIds).getData();
                    List<Long> CustomerGoodsIds = CustomerGoodsList.stream().map(BaseEntity::getId).collect(Collectors.toList());
                    customerGoodsService.changeStatus(CustomerGoodsIds, CustomerGoodsEnum.FROZEN.getCode());
                    List<Integer> needCloseProcessType = Arrays.asList(ProcessTypeEnum.CORE_QUOTA_UPDATE.getCode(),
                            ProcessTypeEnum.FINANCING_QUOTA_UPDATE.getCode());
                    //查询工作流记录
                    List<BusinessProcessProgress> businessProcessProgresses = businessProcessProgressService.listRunningByProcessTypeByFinanceIds(needCloseProcessType, changQuotaIds);
                    //关闭正在调整额度的流程
                    businessProcessProgressService.closeQuotaChange(businessProcessProgresses);
                }

            }
        }
    }


}
