-- 业务录入表
CREATE TABLE `jrzh_business_entry` (
  `id` bigint(64) NOT NULL COMMENT '主键',
  `tenant_id` varchar(12) DEFAULT '000000' COMMENT '租户ID',
  `customer_id` bigint(64) NOT NULL COMMENT '融资客户ID',
  `customer_name` varchar(100) NOT NULL COMMENT '融资客户名称',
  `order_id` bigint(64) DEFAULT NULL COMMENT '订单ID',
  `customer_material_id` bigint(64) DEFAULT NULL COMMENT '客户资料ID',
  `finance_apply_id` bigint(64) DEFAULT NULL COMMENT '融资申请ID',
  `business_type` varchar(20) NOT NULL COMMENT '业务类型：CREDIT-授信，FINANCING-融资，CREDIT_FINANCING-授支一体',
  `product_id` bigint(64) DEFAULT NULL COMMENT '产品ID',
  `product_name` varchar(100) DEFAULT NULL COMMENT '产品名称',
  `business_status` varchar(20) NOT NULL DEFAULT 'IN_PROGRESS' COMMENT '业务状态：IN_PROGRESS-进行中，COMPLETED-已完成，CANCELLED-已取消',
  `business_node` varchar(50) NOT NULL COMMENT '业务节点',
  `business_node_name` varchar(100) NOT NULL COMMENT '业务节点名称',
  `apply_amount` decimal(20,2) DEFAULT NULL COMMENT '申请金额',
  `approved_amount` decimal(20,2) DEFAULT NULL COMMENT '批准金额',
  `current_step` int(11) DEFAULT 1 COMMENT '当前步骤',
  `total_steps` int(11) DEFAULT 1 COMMENT '总步骤数',
  `progress_rate` decimal(5,2) DEFAULT 0.00 COMMENT '进度百分比',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_user` bigint(64) DEFAULT NULL COMMENT '创建人',
  `create_dept` bigint(64) DEFAULT NULL COMMENT '创建部门',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_user` bigint(64) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `status` int(2) DEFAULT 1 COMMENT '业务状态',
  `is_deleted` int(2) DEFAULT 0 COMMENT '是否已删除',
  PRIMARY KEY (`id`),
  KEY `idx_customer_id` (`customer_id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_customer_material_id` (`customer_material_id`),
  KEY `idx_finance_apply_id` (`finance_apply_id`),
  KEY `idx_business_type` (`business_type`),
  KEY `idx_business_status` (`business_status`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='业务录入表';

-- 外键约束注释（实际外键约束根据业务需要决定是否添加）
-- 注意：以下外键约束仅为注释说明，实际使用时需要根据具体业务场景决定是否启用
-- ALTER TABLE `jrzh_business_entry` ADD CONSTRAINT `fk_business_entry_order` FOREIGN KEY (`order_id`) REFERENCES `jrzh_trading_order_data` (`id`);
-- ALTER TABLE `jrzh_business_entry` ADD CONSTRAINT `fk_business_entry_customer_material` FOREIGN KEY (`customer_material_id`) REFERENCES `jrzh_customer_material` (`id`);
-- ALTER TABLE `jrzh_business_entry` ADD CONSTRAINT `fk_business_entry_finance_apply` FOREIGN KEY (`finance_apply_id`) REFERENCES `jrzh_finance_apply` (`id`);


